import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { RebateType } from '@/types/common';
import { getPriceString } from '@/utils';
import { Flex } from '@/libs/ui/Flex/Flex';
import { EstimateRebateProgress } from '../EstimateRebateProgress/EstimateRebateProgress';
import { getRemainingDays } from '../../utils/getRemainingDays';
import dayjs from 'dayjs';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import { SuggestedOfferList } from '@/libs/products/components/SuggestedOfferList/SuggestedOffersList';
import { EstimateRebateCTA } from '../EstimateRebateCTA/EstimateRebateCTA';

interface RebatePanelProps {
  rebate?: RebateType;
}

// Mock rebate data for demonstration
const mockRebate: RebateType = {
  id: 'mock-rebate-1',
  currentRebatePercent: 5,
  nextTierRebatePercent: 10,
  nextTierMinimumSpendAmountThreshold: '50000',
  currentSpendAmount: '25000',
  estimatedRebateAmount: '1250',
  promotion: {
    id: 'mock-promotion-1',
    name: 'Medical Equipment Rebate Program',
    keyword: 'medical-equipment',
    startedAt: '2024-01-01',
    endedAt: '2024-12-31',
    vendor: {
      id: 'mock-vendor-1',
      name: 'MedSupply Corp',
      imageUrl: 'https://via.placeholder.com/40x40',
    },
  },
  suggestedProductOffers: [],
};

export const RebatePanel = ({ rebate = mockRebate }: RebatePanelProps) => {
  const remainingDays = getRemainingDays(rebate.promotion.endedAt);
  const startDate = dayjs(rebate.promotion.startedAt).format(
    DEFAULT_DISPLAY_DATE_FORMAT,
  );
  const endDate = dayjs(rebate.promotion.endedAt).format(
    DEFAULT_DISPLAY_DATE_FORMAT,
  );
  const hasSuggestedOffers = rebate.suggestedProductOffers.length > 0;
  const noMoneySpent = rebate.currentSpendAmount === '0.00';

  return (
    <CollapsiblePanel
      header={
        <Flex
          p="md"
          pr={hasSuggestedOffers ? '4rem' : 'md'}
          align="center"
          justify="space-between"
          w="100%"
        >
          <Flex align="center">
            <div>
              <div
                className="text-xs mb-2"
                style={{ color: remainingDays < 7 ? '#A31838' : '#ED7F02' }}
              >
                <span className="font-bold">
                  {remainingDays}
                </span>{' '}
                {remainingDays > 1 ? 'days ' : 'day '} remaining in rebate
                period
              </div>
              <div className="text-base font-bold mb-2 w-[260px]">
                {rebate.promotion.name}
              </div>
              <Flex align="center" gap="0.25rem">
                <span className="text-xs text-gray-600">
                  Start:{' '}
                  <span className="text-gray-800 font-bold">
                    {startDate}
                  </span>
                </span>
                {' - '}
                <span className="text-xs text-gray-600">
                  End:{' '}
                  <span className="text-gray-800 font-bold">
                    {endDate}
                  </span>
                </span>
              </Flex>
            </div>
            <div className="w-px h-full bg-gray-300 mx-6" />
            <div>
              <div className="text-xs w-[100px]" style={{ color: '#344054' }}>
                Amount spent
              </div>
              <div className="font-bold text-sm mt-2" style={{ color: '#344054' }}>
                {getPriceString(rebate.currentSpendAmount)}
              </div>
            </div>
            <div className="w-px h-full bg-gray-300 mx-6" />
            <div>
              <div className="text-xs w-[100px]" style={{ color: '#344054' }}>
                Estimated rebate
              </div>
              <div className="font-bold text-sm mt-2" style={{ color: '#344054' }}>
                {getPriceString(rebate.estimatedRebateAmount)}
              </div>
            </div>
          </Flex>
          {noMoneySpent ? (
            <EstimateRebateCTA
              currentRebatePercent={rebate.currentRebatePercent}
              promotionKeyWord={rebate.promotion.keyword}
            />
          ) : (
            <div className="ml-6">
              <EstimateRebateProgress {...rebate} />
            </div>
          )}
        </Flex>
      }
      content={
        hasSuggestedOffers ? (
          <div className="bg-white px-4 pb-4">
            <div className="bg-black/[0.02] p-4">
              <SuggestedOfferList offers={rebate.suggestedProductOffers} />
            </div>
          </div>
        ) : null
      }
      variant="clean"
    />
  );
};
