import { Flex } from '@/libs/ui/Flex/Flex';
import { Progress } from '@/libs/ui/Progress/Progress';
import styles from './EstimateRebateProgress.module.css';
import { getPriceString } from '@/utils';
import clsx from 'clsx';

interface EstimateRebateProgressProps {
  currentRebatePercent?: number;
  nextTierRebatePercent?: number;
  nextTierMinimumSpendAmountThreshold?: string;
  currentSpendAmount?: string;
}
export const EstimateRebateProgress = ({
  currentRebatePercent = 5,
  nextTierRebatePercent = 10,
  nextTierMinimumSpendAmountThreshold = '50000',
  currentSpendAmount = '25000',
}: EstimateRebateProgressProps) => {
  const progressPercentage =
    (+currentSpendAmount * 100) / +nextTierMinimumSpendAmountThreshold;

  const isCompleted =
    progressPercentage >= 100 || +nextTierMinimumSpendAmountThreshold === 0;

  const startPercentage = nextTierRebatePercent ? currentRebatePercent : 0;
  const endPercentage = nextTierRebatePercent
    ? nextTierRebatePercent
    : currentRebatePercent;

  return (
    <Flex
      className={styles.container}
      align="center"
      h="2.125rem"
      pl="1.125rem"
      pr="0.625rem"
    >
      <span className="text-xs whitespace-nowrap">
        {isCompleted ? (
          <>Earning Full Rebate 🎉</>
        ) : (
          <>
            <span className="mr-1 font-bold">
              {getPriceString(
                +nextTierMinimumSpendAmountThreshold - +currentSpendAmount,
              )}
            </span>
            until next tier
          </>
        )}
      </span>
      <div className="h-4">
        <div className="mx-4 h-full w-px bg-black/15" />
      </div>
      <Flex align="center" gap="0.5rem">
        {isCompleted ? (
          <Flex className={clsx(styles.pill, styles.active)}>
            {currentRebatePercent}%
          </Flex>
        ) : (
          <>
            <Flex className={clsx(styles.pill, styles.active)}>
              {startPercentage}%
            </Flex>
            <Progress
              radius="lg"
              value={progressPercentage}
              minWidth="5rem"
              color="#518EF8"
            />
            <Flex className={styles.pill}>{endPercentage}%</Flex>
          </>
        )}
      </Flex>
    </Flex>
  );
};
