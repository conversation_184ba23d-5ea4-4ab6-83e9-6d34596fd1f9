import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { useState } from 'react';
import { AMAZON_DISCOUNT_URL } from '../../constants';
import { t } from 'i18next';

const urls = [
  {
    label: t('client.vendors.amazon.existingAccount'),
    url: AMAZON_DISCOUNT_URL,
  },
  {
    label: t('client.vendors.amazon.noAccount'),
    url: AMAZON_DISCOUNT_URL,
  },
  {
    label: t('client.vendors.amazon.convertAccount'),
    url: AMAZON_DISCOUNT_URL,
  },
];

interface StepOneProps {
  onNext: () => void;
}

export const ApplyDiscount = ({ onNext }: StepOneProps) => {
  const [checked, setChecked] = useState(false);

  return (
    <div className="flex w-full flex-col">
      {urls.map((item) => (
        <Button
          key={item.label}
          variant="white"
          className="mb-2"
          href={item.url}
          target="_blank"
        >
          {item.label}
        </Button>
      ))}
      <label className="mt-2 mb-2 flex gap-4">
        <Checkbox
          checked={checked}
          onChange={(e) => setChecked(e.target.checked)}
        />
        <p className="m-0 text-xs">
          {t('client.vendors.amazon.discountApplied')}
        </p>
      </label>
      <Button
        variant="white"
        className="mb-4"
        onClick={onNext}
        disabled={!checked}
      >
        <span className="font-semibold">
          {t('client.vendors.amazon.nextStep')}
        </span>
      </Button>
    </div>
  );
};
