import { CSSProperties } from 'react';

export function getPercentage(value?: number, maxValue?: number) {
  if (!value || !maxValue) {
    return;
  }

  return (100 * value) / maxValue;
}

interface ProgressColors {
  bgColor: string;
  labelBgColor: string;
  shadowColor: string;
}

export function getProgressColor(progress?: number): ProgressColors {
  if (!progress) {
    return {
      bgColor: '#868e96',
      labelBgColor: '#495057',
      shadowColor: '0 2px 4px 0 #00000026',
    };
  }

  if (progress < 75) {
    return {
      bgColor: '#2b8a3e',
      labelBgColor: '#d3f9d8',
      shadowColor: '0 2px 4px 0 #00000026',
    };
  }

  if (progress < 90) {
    return {
      bgColor: '#fab005',
      labelBgColor: '#fff3bf',
      shadowColor: '0 2px 4px 0 #00000026',
    };
  }

  if (progress <= 99) {
    return {
      bgColor: '#ffa8a8',
      labelBgColor: '#ffe0e0',
      shadowColor: '0 2px 4px 0 #00000026',
    };
  }

  return {
    bgColor: '#ffa8a8',
    labelBgColor: '#ffe0e0',
    shadowColor: '0px 0px 10px 0px #ffe0e0',
  };
}
