import React from 'react';
import { useTranslation } from 'react-i18next';

import { getPriceString } from '@/utils';
import { Tooltip } from '@/libs/ui/Tooltip';
import { Progress } from '@/libs/ui/Progress/Progress';
import QuestionIcon from '@/assets/images/question.svg?react';

import { getPercentage, getProgressColor } from './utils';
import styles from './budget-line.module.css';

interface BudgetLineProps {
  name?: string;
  value?: number;
  maxValue?: number;
  isDynamicBudget?: boolean;
  budgetUsed?: string;
  desc?: string;
  budgetUsedType?: '%' | '$';
}

const PRICE_CONFIG = {
  formatter: new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0,
  }),
};

export const BudgetLine = (props: BudgetLineProps) => {
  const {
    value = 75000,
    maxValue = 100000,
    name = 'Medical Supplies',
    isDynamicBudget = false,
    budgetUsed = '$75,000',
    desc = 'Budget used for medical supplies and equipment',
    budgetUsedType = '$',
  } = props;

  const { t } = useTranslation();

  const progress = getPercentage(value, maxValue) || 0;

  const colors = getProgressColor(progress);

  return (
    <div style={colors} className={styles.box}>
      <div className={styles.topLine}>
        <Tooltip label={desc}>
          <QuestionIcon />
        </Tooltip>

        <span className="text-xs text-black">
          {isDynamicBudget ? budgetUsedType : null}
          {budgetUsed}
          {isDynamicBudget ? null : budgetUsedType}

          {t('client.cart.budgetUsed')}
        </span>
      </div>

      <Progress
        value={progress || 0}
        height="h-1.5"
        radius="lg"
        className={styles.root}
        color={colors['--bgColor'] as string}
      />

      <div className={styles.text}>
        <span className="text-xs text-black">
          {name}{' '}
          {isDynamicBudget ? `${value}%` : getPriceString(value, PRICE_CONFIG)}
        </span>

        <span className="text-xs text-black">
          {isDynamicBudget
            ? `${maxValue}%`
            : getPriceString(maxValue, PRICE_CONFIG)}{' '}
          {t('client.cart.budgetTarget')}
        </span>
      </div>
    </div>
  );
};
