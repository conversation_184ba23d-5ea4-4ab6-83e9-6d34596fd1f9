import { mergeClasses } from '@/utils/tailwind';

interface ProgressProps {
  value: number;
  color?: string;
  height?: string;
  className?: string;
  minWidth?: string;
  radius?: 'sm' | 'md' | 'lg' | 'xl';
}

const radiusClasses = {
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
};

export const Progress = ({
  value,
  color = '#518EF8',
  height = 'h-2',
  className,
  minWidth,
  radius = 'lg',
}: ProgressProps) => {
  const clampedValue = Math.min(Math.max(value || 0, 0), 100);

  return (
    <div
      className={mergeClasses(
        'relative w-full overflow-hidden bg-gray-200 shadow-inner',
        height,
        radiusClasses[radius],
        className,
      )}
      style={{ minWidth }}
      role="progressbar"
      aria-valuenow={clampedValue}
      aria-valuemin={0}
      aria-valuemax={100}
    >
      <div
        className={mergeClasses(
          'h-full transition-all duration-500 ease-out',
          radiusClasses[radius],
        )}
        style={{
          width: `${clampedValue}%`,
          backgroundColor: color,
          boxShadow:
            'inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(255, 255, 255, 0.8)',
        }}
      />
    </div>
  );
};
