import { Progress as RadixProgress } from 'radix-ui';
import { mergeClasses } from '@/utils/tailwind';

interface ProgressProps {
  value: number;
  color?: string;
  height?: string;
  className?: string;
  minWidth?: string;
  radius?: 'sm' | 'md' | 'lg' | 'xl';
}

const radiusClasses = {
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
};

export const Progress = ({
  value,
  color = '#518EF8',
  height = 'h-2',
  className,
  minWidth,
  radius = 'lg',
}: ProgressProps) => {
  return (
    <RadixProgress.Root
      className={mergeClasses(
        'relative w-full overflow-hidden bg-gray-200 shadow-inner',
        height,
        radiusClasses[radius],
        className,
      )}
      style={{ minWidth }}
      value={value}
    >
      <RadixProgress.Indicator
        className={mergeClasses(
          'h-full w-full shadow-sm transition-transform duration-500 ease-out motion-reduce:transition-none',
          radiusClasses[radius],
        )}
        style={{
          backgroundColor: color,
          transform: `translateX(-${100 - (value || 0)}%)`,
          boxShadow:
            'inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(255, 255, 255, 0.8)',
        }}
      />
    </RadixProgress.Root>
  );
};
